// Script pour uploader tous les scripts vers Firestore
// Usage: node scripts/uploadScriptsToFirestore.js

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, doc, setDoc, writeBatch } from 'firebase/firestore';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Pour obtenir __dirname dans un module ES
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyABy8bGDxVU-sM2nqfsp3jDm8JPtg_v4kM",
  authDomain: "piknowkyo-777.firebaseapp.com",
  projectId: "piknowkyo-777",
  storageBucket: "piknowkyo-777.firebasestorage.app",
  messagingSenderId: "375619599814",
  appId: "1:375619599814:web:9ece9c5c2ce600a8c206c7",
  measurementId: "G-DSXRMZ4JP2"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Langues supportées
const LANGUAGES = ['fr', 'en', 'es'];

// Catégories pour organiser les scripts
const CATEGORIES = {
  'hypnosis': 'Hypnose',
  'meditation': 'Méditation', 
  'training': 'Formation',
  'visualization': 'Visualisation',
  'story': 'Histoire',
  'silence': 'Silence'
};

// Tags pour organiser par thème
const TAG_CATEGORIES = {
  'développement-personnel': ['estime-de-soi', 'confiance-en-soi', 'motivation', 'croissance-personnelle'],
  'santé-bien-être': ['relaxation', 'sommeil', 'guérison', 'post-opératoire', 'forme-physique'],
  'émotionnel': ['deuil', 'acceptation', 'libération', 'paix-intérieure', 'guérison-émotionnelle'],
  'addictions-blocages': ['addictions', 'blocages-mentaux', 'blocages-énergétiques', 'croyances-limitantes', 'phobies'],
  'alimentation': ['alimentation-saine', 'gestion-du-poids', 'boulimie', 'anorexie'],
  'abondance-finances': ['abondance', 'richesse', 'finances', 'prospérité', 'manifestation'],
  'spiritualité': ['chakras', 'énergie', 'parapsychologie', 'merkaba', 'intuition'],
  'enfants': ['enfants', 'hypnose-enfants', 'tdah', 'peurs'],
  'mémoire-concentration': ['mémoire', 'mémoire-photographique', 'concentration', 'tda', 'clarté']
};

// Fonction pour déterminer la catégorie d'un script basé sur ses tags
function determineCategory(tags) {
  for (const [category, categoryTags] of Object.entries(TAG_CATEGORIES)) {
    if (tags.some(tag => categoryTags.includes(tag))) {
      return category;
    }
  }
  return 'autres';
}

// Fonction pour lire un fichier JSON
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Erreur lors de la lecture de ${filePath}:`, error.message);
    return null;
  }
}

// Fonction pour uploader un script vers Firestore
async function uploadScript(scriptData, language) {
  try {
    const category = determineCategory(scriptData.tags || []);
    
    // Structure du document dans Firestore
    const firestoreDoc = {
      // Métadonnées de base
      id: scriptData.id,
      title: scriptData.title,
      description: scriptData.description || '',
      benefits: scriptData.benefits || '',
      
      // Informations de session
      type: scriptData.type,
      language: language,
      durationMinutes: scriptData.durationMinutes || 0,
      estimatedDuration: scriptData.estimatedDuration || scriptData.durationMinutes || 0,
      
      // Organisation
      category: category,
      tags: scriptData.tags || [],
      
      // Monétisation
      isPremium: scriptData.isPremium || false,
      price: scriptData.price || 0.0,
      
      // Métadonnées temporelles
      createdAt: scriptData.createdAt || new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString(),
      version: 1,
      isNew: scriptData.isNew || false,
      
      // Contenu du script
      script: scriptData.script || [],
      
      // Métadonnées additionnelles
      imageUrl: scriptData.imageUrl || null,
      audioUrl: scriptData.audioUrl || null,
      
      // Statistiques (initialisées à 0)
      playCount: 0,
      rating: 0,
      ratingCount: 0
    };

    // Chemin du document : scripts/{language}_{scriptId}
    const docId = `${language}_${scriptData.id}`;
    const docRef = doc(db, 'scripts', docId);
    
    await setDoc(docRef, firestoreDoc);
    console.log(`✅ Script uploadé: ${language}/${scriptData.id}`);
    
    return true;
  } catch (error) {
    console.error(`❌ Erreur lors de l'upload de ${language}/${scriptData.id}:`, error.message);
    return false;
  }
}

// Fonction pour uploader le manifeste d'une langue
async function uploadManifest(language, sessions) {
  try {
    const manifestDoc = {
      language: language,
      sessions: sessions,
      totalSessions: sessions.length,
      lastUpdated: new Date().toISOString(),
      version: 1
    };

    const docRef = doc(db, 'manifests', language);
    await setDoc(docRef, manifestDoc);
    console.log(`✅ Manifeste uploadé pour ${language}: ${sessions.length} sessions`);
    
    return true;
  } catch (error) {
    console.error(`❌ Erreur lors de l'upload du manifeste ${language}:`, error.message);
    return false;
  }
}

// Fonction principale
async function uploadAllScripts() {
  console.log('🚀 Début de l\'upload des scripts vers Firestore...\n');
  
  let totalUploaded = 0;
  let totalErrors = 0;

  for (const language of LANGUAGES) {
    console.log(`📁 Traitement de la langue: ${language}`);
    
    // Lire le manifeste
    const manifestPath = path.join(__dirname, '..', 'public', 'assets', 'manifests', `manifest_${language}.json`);
    const manifest = readJsonFile(manifestPath);
    
    if (!manifest || !manifest.sessions) {
      console.error(`❌ Impossible de lire le manifeste pour ${language}`);
      continue;
    }

    // Uploader chaque script
    const scriptsDir = path.join(__dirname, '..', 'public', 'assets', 'sessionScripts', language);
    
    for (const session of manifest.sessions) {
      const scriptPath = path.join(scriptsDir, `${session.id}.json`);
      const scriptData = readJsonFile(scriptPath);
      
      if (scriptData) {
        const success = await uploadScript(scriptData, language);
        if (success) {
          totalUploaded++;
        } else {
          totalErrors++;
        }
      } else {
        console.error(`❌ Script non trouvé: ${scriptPath}`);
        totalErrors++;
      }
    }

    // Uploader le manifeste
    await uploadManifest(language, manifest.sessions);
    
    console.log(`✅ Langue ${language} terminée\n`);
  }

  console.log('📊 Résumé:');
  console.log(`✅ Scripts uploadés avec succès: ${totalUploaded}`);
  console.log(`❌ Erreurs: ${totalErrors}`);
  console.log(`📁 Manifestes uploadés: ${LANGUAGES.length}`);
  console.log('\n🎉 Upload terminé !');
}

// Exécuter le script
uploadAllScripts().catch(console.error);
