// src/components/ReusableModal.tsx
import React, { ReactNode } from 'react';
import styled, { DefaultTheme, useTheme } from 'styled-components'; // useTheme pour accéder au thème
import { FiAlertCircle, FiX, FiLoader } from 'react-icons/fi';

// --- Props pour la Modale ---
interface ReusableModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  titleIcon?: ReactNode; // Pour une icône optionnelle dans le titre
  children: ReactNode; // Contenu principal de la modale
  footerContent?: ReactNode; // Pour les boutons d'action ou autre contenu de pied de page
  isLoading?: boolean; // Pour désactiver les interactions pendant le chargement
  maxWidth?: string; // Permettre de personnaliser la largeur max
  closeOnOverlayClick?: boolean; // Permettre de fermer en cliquant sur l'overlay
}

// --- Styled Components pour la Modale ---
const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.6);
  display: ${({ $isOpen }) => $isOpen ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: ${({ $isOpen }) => $isOpen ? 1 : 0};
  transition: opacity 0.3s ease-in-out;
`;

const ModalDialog = styled.div<{ $maxWidth?: string }>`
  background: ${({ theme }) => theme.surface};
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.2);
  max-width: ${({ $maxWidth }) => $maxWidth || '500px'};
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  color: ${({ theme }) => theme.text};
  position: relative; // Pour le bouton de fermeture
  transform: scale(0.95);
  opacity: 0;
  animation: fadeInScale 0.3s forwards ease-out;

  @media (max-width: 768px) {
    width: 95%;
    padding: 1rem;
    max-height: 95vh;
  }

  @media (max-width: 480px) {
    width: 98%;
    padding: 0.75rem;
    border-radius: 8px;
  }

  @keyframes fadeInScale {
    to {
      transform: scale(1);
      opacity: 1;
    }
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};

  h3 {
    color: ${({ theme }) => theme.primary};
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

const CloseButton = styled.button`
  background: transparent;
  border: none;
  color: ${({ theme }) => theme.textMuted};
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  line-height: 1;
  transition: color 0.2s;

  &:hover {
    color: ${({ theme }) => theme.text};
  }
`;

const ModalBody = styled.div`
  margin-bottom: 1.5rem;
  line-height: 1.6;
  font-size: 0.95rem;
  color: ${({ theme }) => theme.textSecondary};
  /* Styles spécifiques au contenu si nécessaire */
  p {
    margin-top: 0;
    &:last-child {
      margin-bottom: 0;
    }
  }
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end; /* Ou center, space-around selon vos besoins par défaut */
  gap: 0.75rem;
  padding-top: 1rem;
  border-top: 1px solid ${({ theme }) => theme.border};
`;

// Le composant Modal réutilisable
const ReusableModal: React.FC<ReusableModalProps> = ({
  isOpen,
  onClose,
  title,
  titleIcon,
  children,
  footerContent,
  isLoading = false,
  maxWidth,
  closeOnOverlayClick = true,
}) => {
  if (!isOpen) {
    return null;
  }

  const handleOverlayClick = () => {
    if (closeOnOverlayClick && !isLoading) {
      onClose();
    }
  };

  return (
    <ModalOverlay $isOpen={isOpen} onClick={handleOverlayClick}>
      <ModalDialog $maxWidth={maxWidth} onClick={(e) => e.stopPropagation()}>
        {(title || typeof onClose === 'function') && (
          <ModalHeader>
            {title && <h3>{titleIcon}{title}</h3>}
            {typeof onClose === 'function' && (
              <CloseButton onClick={onClose} disabled={isLoading} aria-label="Fermer la modale">
                <FiX />
              </CloseButton>
            )}
          </ModalHeader>
        )}
        <ModalBody>
          {children}
        </ModalBody>
        {footerContent && (
          <ModalFooter>
            {footerContent}
          </ModalFooter>
        )}
      </ModalDialog>
    </ModalOverlay>
  );
};

export default ReusableModal;