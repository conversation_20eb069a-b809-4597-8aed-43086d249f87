import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  setDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  writeBatch,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '@/config/firebase';

export interface ScriptSegment {
  text: string;
  pause: number;
  pitch: number;
  rate: number;
}

export interface Script {
  id: string;
  title: string;
  description: string;
  benefits: string;
  type: 'hypnosis' | 'meditation' | 'training' | 'visualization' | 'story' | 'silence';
  language: 'fr' | 'en' | 'es';
  durationMinutes: number;
  estimatedDuration: number;
  category: string;
  tags: string[];
  isPremium: boolean;
  price: number;
  createdAt: string;
  updatedAt: string;
  version: number;
  isNew: boolean;
  script: ScriptSegment[];
  imageUrl?: string;
  audioUrl?: string;
  playCount: number;
  rating: number;
  ratingCount: number;
}

export interface ScriptManifest {
  language: string;
  sessions: Array<{
    id: string;
    title: string;
    type: string;
    estimatedDuration: number;
    tags: string[];
    imageUrl?: string;
    isPremium: boolean;
  }>;
  totalSessions: number;
  lastUpdated: string;
  version: number;
}

// Catégories pour organiser les scripts
const TAG_CATEGORIES: Record<string, string[]> = {
  'développement-personnel': ['estime-de-soi', 'confiance-en-soi', 'motivation', 'croissance-personnelle'],
  'santé-bien-être': ['relaxation', 'sommeil', 'guérison', 'post-opératoire', 'forme-physique'],
  'émotionnel': ['deuil', 'acceptation', 'libération', 'paix-intérieure', 'guérison-émotionnelle'],
  'addictions-blocages': ['addictions', 'blocages-mentaux', 'blocages-énergétiques', 'croyances-limitantes', 'phobies'],
  'alimentation': ['alimentation-saine', 'gestion-du-poids', 'boulimie', 'anorexie'],
  'abondance-finances': ['abondance', 'richesse', 'finances', 'prospérité', 'manifestation'],
  'spiritualité': ['chakras', 'énergie', 'parapsychologie', 'merkaba', 'intuition'],
  'enfants': ['enfants', 'hypnose-enfants', 'tdah', 'peurs'],
  'mémoire-concentration': ['mémoire', 'mémoire-photographique', 'concentration', 'tda', 'clarté']
};

// Fonction pour déterminer la catégorie d'un script basé sur ses tags
function determineCategory(tags: string[]): string {
  for (const [category, categoryTags] of Object.entries(TAG_CATEGORIES)) {
    if (tags.some(tag => categoryTags.includes(tag))) {
      return category;
    }
  }
  return 'autres';
}

class ScriptService {
  // Récupérer tous les scripts d'une langue
  async getScriptsByLanguage(language: 'fr' | 'en' | 'es'): Promise<Script[]> {
    try {
      const scriptsQuery = query(
        collection(db, 'scripts'),
        where('language', '==', language),
        orderBy('title')
      );
      
      const querySnapshot = await getDocs(scriptsQuery);
      const scripts: Script[] = [];
      
      querySnapshot.forEach((doc) => {
        scripts.push({ ...doc.data() } as Script);
      });
      
      return scripts;
    } catch (error) {
      console.error('Erreur lors de la récupération des scripts:', error);
      throw error;
    }
  }

  // Récupérer un script spécifique
  async getScript(language: string, scriptId: string): Promise<Script | null> {
    try {
      const docId = `${language}_${scriptId}`;
      const docRef = doc(db, 'scripts', docId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return docSnap.data() as Script;
      }
      
      return null;
    } catch (error) {
      console.error('Erreur lors de la récupération du script:', error);
      throw error;
    }
  }

  // Sauvegarder un script
  async saveScript(script: Partial<Script>, isNew: boolean = false): Promise<Script> {
    try {
      if (!script.id || !script.language) {
        throw new Error('ID et langue du script requis');
      }

      const docId = `${script.language}_${script.id}`;
      const docRef = doc(db, 'scripts', docId);
      
      // Récupérer la version actuelle si le script existe
      let currentVersion = 1;
      let existingData: any = {};
      
      if (!isNew) {
        const currentDoc = await getDoc(docRef);
        if (currentDoc.exists()) {
          existingData = currentDoc.data();
          currentVersion = (existingData.version || 1) + 1;
        }
      }

      // Déterminer la catégorie automatiquement
      const category = script.tags ? determineCategory(script.tags) : 'autres';

      // Préparer les données du script
      const scriptData: Script = {
        // Données de base
        id: script.id,
        title: script.title || '',
        description: script.description || '',
        benefits: script.benefits || '',
        
        // Type et langue
        type: script.type || 'meditation',
        language: script.language,
        durationMinutes: script.durationMinutes || 0,
        estimatedDuration: script.estimatedDuration || script.durationMinutes || 0,
        
        // Organisation
        category,
        tags: script.tags || [],
        
        // Monétisation
        isPremium: script.isPremium || false,
        price: script.price || 0,
        
        // Métadonnées temporelles
        createdAt: script.createdAt || new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString(),
        version: currentVersion,
        isNew: script.isNew || false,
        
        // Contenu
        script: script.script || [],
        
        // URLs optionnelles
        imageUrl: script.imageUrl || null,
        audioUrl: script.audioUrl || null,
        
        // Statistiques (conserver les existantes ou initialiser)
        playCount: existingData.playCount || 0,
        rating: existingData.rating || 0,
        ratingCount: existingData.ratingCount || 0
      };

      await setDoc(docRef, scriptData);
      
      // Mettre à jour le manifeste
      await this.updateManifest(script.language);
      
      return scriptData;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du script:', error);
      throw error;
    }
  }

  // Supprimer un script
  async deleteScript(language: string, scriptId: string): Promise<void> {
    try {
      const docId = `${language}_${scriptId}`;
      const docRef = doc(db, 'scripts', docId);
      
      await deleteDoc(docRef);
      
      // Mettre à jour le manifeste
      await this.updateManifest(language);
    } catch (error) {
      console.error('Erreur lors de la suppression du script:', error);
      throw error;
    }
  }

  // Mettre à jour le manifeste d'une langue
  async updateManifest(language: string): Promise<void> {
    try {
      const scripts = await this.getScriptsByLanguage(language as 'fr' | 'en' | 'es');
      
      const sessions = scripts.map(script => ({
        id: script.id,
        title: script.title,
        type: script.type,
        estimatedDuration: script.estimatedDuration,
        tags: script.tags,
        imageUrl: script.imageUrl || null,
        isPremium: script.isPremium
      }));

      const manifest: ScriptManifest = {
        language,
        sessions,
        totalSessions: sessions.length,
        lastUpdated: new Date().toISOString(),
        version: 1
      };

      const manifestRef = doc(db, 'manifests', language);
      await setDoc(manifestRef, manifest);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du manifeste:', error);
      throw error;
    }
  }

  // Synchroniser tous les scripts vers Firestore
  async syncAllScriptsToFirestore(): Promise<void> {
    try {
      const languages = ['fr', 'en', 'es'];
      
      for (const language of languages) {
        await this.updateManifest(language);
      }
    } catch (error) {
      console.error('Erreur lors de la synchronisation:', error);
      throw error;
    }
  }

  // Récupérer les statistiques des scripts
  async getScriptStats(): Promise<{
    totalScripts: number;
    scriptsByLanguage: Record<string, number>;
    scriptsByType: Record<string, number>;
    scriptsByCategory: Record<string, number>;
  }> {
    try {
      const allScripts: Script[] = [];
      const languages = ['fr', 'en', 'es'];
      
      for (const language of languages) {
        const scripts = await this.getScriptsByLanguage(language as 'fr' | 'en' | 'es');
        allScripts.push(...scripts);
      }

      const scriptsByLanguage: Record<string, number> = {};
      const scriptsByType: Record<string, number> = {};
      const scriptsByCategory: Record<string, number> = {};

      allScripts.forEach(script => {
        // Par langue
        scriptsByLanguage[script.language] = (scriptsByLanguage[script.language] || 0) + 1;
        
        // Par type
        scriptsByType[script.type] = (scriptsByType[script.type] || 0) + 1;
        
        // Par catégorie
        scriptsByCategory[script.category] = (scriptsByCategory[script.category] || 0) + 1;
      });

      return {
        totalScripts: allScripts.length,
        scriptsByLanguage,
        scriptsByType,
        scriptsByCategory
      };
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }

  // Dupliquer un script vers une autre langue
  async duplicateScript(sourceScript: Script, targetLanguage: 'fr' | 'en' | 'es', newId: string): Promise<Script> {
    try {
      const duplicatedScript: Partial<Script> = {
        ...sourceScript,
        id: newId,
        language: targetLanguage,
        title: `${sourceScript.title} (${targetLanguage})`,
        version: 1,
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString(),
        playCount: 0,
        rating: 0,
        ratingCount: 0
      };

      return await this.saveScript(duplicatedScript, true);
    } catch (error) {
      console.error('Erreur lors de la duplication du script:', error);
      throw error;
    }
  }
}

export const scriptService = new ScriptService();
