import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import { createSelector } from '@reduxjs/toolkit';
import type { RootState, AppDispatch } from './index';

// Hooks typés pour Redux
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Sélecteurs mémorisés pour éviter les re-renders inutiles
const selectAuthState = (state: RootState) => state.auth;
const selectSessionsState = (state: RootState) => state.sessions;
const selectJournalState = (state: RootState) => state.journal;
const selectAudioAssetsState = (state: RootState) => state.audioAssets;
const selectSyncState = (state: RootState) => state.sync;
const selectNetworkState = (state: RootState) => state.network;

const selectAuthData = createSelector(
  [selectAuthState],
  (auth) => ({
    user: auth.user,
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    error: auth.error,
    lastSyncTimestamp: auth.lastSyncTimestamp,
  })
);

const selectSessionsData = createSelector(
  [selectSessionsState],
  (sessions) => ({
    sessions: sessions.sessions,
    currentSession: sessions.currentSession,
    isLoading: sessions.isLoading,
    error: sessions.error,
    lastSyncTimestamp: sessions.lastSyncTimestamp,
    pendingChanges: sessions.pendingChanges,
  })
);

const selectJournalData = createSelector(
  [selectJournalState],
  (journal) => ({
    entries: journal.entries,
    isLoading: journal.isLoading,
    error: journal.error,
    lastSyncTimestamp: journal.lastSyncTimestamp,
    pendingChanges: journal.pendingChanges,
  })
);

const selectAudioAssetsData = createSelector(
  [selectAudioAssetsState],
  (audioAssets) => ({
    assets: audioAssets.assets,
    manifest: audioAssets.manifest,
    isLoading: audioAssets.isLoading,
    error: audioAssets.error,
    lastSyncTimestamp: audioAssets.lastSyncTimestamp,
    pendingChanges: audioAssets.pendingChanges,
  })
);

const selectSyncData = createSelector(
  [selectSyncState],
  (sync) => ({
    isOnline: sync.isOnline,
    isSyncing: sync.isSyncing,
    lastSyncTimestamp: sync.lastSyncTimestamp,
    syncErrors: sync.syncErrors,
    pendingSyncCount: sync.pendingSyncCount,
    syncStatus: sync.syncStatus,
    conflicts: sync.conflictResolution.conflicts,
    conflictStrategy: sync.conflictResolution.strategy,
  })
);

const selectNetworkData = createSelector(
  [selectNetworkState],
  (network) => ({
    isOnline: network.isOnline,
    connectionType: network.connectionType,
    isSlowConnection: network.isSlowConnection,
    lastOnlineTimestamp: network.lastOnlineTimestamp,
    offlineDuration: network.offlineDuration,
  })
);

// Hooks personnalisés utilisant les sélecteurs mémorisés
export const useAuth = () => {
  return useAppSelector(selectAuthData);
};

export const useSessions = () => {
  return useAppSelector(selectSessionsData);
};

export const useJournal = () => {
  return useAppSelector(selectJournalData);
};

export const useAudioAssets = () => {
  return useAppSelector(selectAudioAssetsData);
};

export const useSync = () => {
  return useAppSelector(selectSyncData);
};

export const useNetwork = () => {
  return useAppSelector(selectNetworkData);
};

// Sélecteur mémorisé pour le statut hors ligne
const selectOfflineStatus = createSelector(
  [selectNetworkData, selectSyncData],
  (network, sync) => ({
    isOffline: !network.isOnline,
    hasPendingChanges: sync.pendingSyncCount > 0,
    canSync: network.isOnline && !sync.isSyncing,
    syncStatus: sync.syncStatus,
    offlineDuration: network.offlineDuration,
  })
);

// Hooks composés pour des cas d'usage spécifiques
export const useOfflineStatus = () => {
  return useAppSelector(selectOfflineStatus);
};
